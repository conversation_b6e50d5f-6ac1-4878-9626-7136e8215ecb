import React, { useState, useRef, useEffect } from 'react';
import { YTHLocalization, YTHList, YTHForm, YTHDialog } from 'yth-ui';
import { Button, Modal, Space, message } from 'antd';
import locales from '@/locales';
import dayjs from 'dayjs';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { queryEnvQualityMonitorList } from '@/service/envApi';
import analysisApi from '@/service/analysisApi';
import style from './index.module.less';

type ModalKey = 'add' | 'edit';
type ModalValue = '新增' | '编辑';

/** 弹窗类型 显示标题 */
const modalTypeData: Record<ModalKey, ModalValue> = {
  add: '新增',
  edit: '编辑',
};
/**
 * @description 分析报告列表
 * @returns
 */
const ReportList: React.FC = () => {
  const listActionRef = useRef<ActionType>();
  const listAction = YTHList.createAction();
  const form = React.useMemo(() => YTHForm.createForm({}), []);
  const [visiable, setVisiable] = useState<boolean>(false);
  const [modalType, setModalType] = useState<ModalKey>('add');

  /** 列表列配置 */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'name',
      title: '报告名称',
      width: 180,
      query: true,
      display: true,
    },
    {
      dataIndex: 'name2',
      title: '报告月份',
      width: 180,
      query: true,
      display: true,
      componentName: 'DatePicker',
      componentProps: {
        precision: 'month',
        formatter: 'YYYY-MM',
        p_props: {
          placeholder: '请选择',
        },
      },
      render: (_v, record) => {
        return record.name || '-';
      },
    },
    {
      dataIndex: 'name',
      title: '操作人',
      width: 180,
      query: false,
      display: true,
    },
    {
      dataIndex: 'name',
      title: '生成时间',
      width: 180,
      query: false,
      display: true,
    },
  ];

  /** 提交保存 */
  const submitAddData = async (data) => {
    const res = await analysisApi.addReport(data);
    if (res && res.code && res.code === 200) {
      message.success('新增数据成功');
    }
  };

  /** form 保存 */
  const formSave = () => {
    form.validate().then(() => {
      const submitData = {
        ...form.values,
      };
      submitAddData(submitData);
    });
  };

  // 确认删除数据
  const confirmDelete = async (row) => {
    const res = await analysisApi.deleteReport(row.id);
    if (res && res.code && res.code === 200) {
      message.success('删除数据成功');
    }
  };

  useEffect(() => {
    if (visiable) {
      form.setValues({
        reportTime: dayjs().format('YYYY-MM'),
        reportName: '',
      });
    }
  }, [visiable]);

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="envQualityOnLineMonitorList"
        action={listAction}
        actionRef={listActionRef}
        showRowSelection={false}
        operation={[
          {
            element: (
              <div>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    setVisiable(true);
                    setModalType('add');
                  }}
                >
                  新增
                </Button>
              </div>
            ),
          },
        ]}
        extraOperation={[{ element: <Button size="small">返回统计分析</Button> }]}
        listKey="id"
        request={async (filter, pagination) => {
          const resData = await queryEnvQualityMonitorList({
            descs: [''],
            condition: filter,
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
          });
          if (resData.code && resData.code === 200) {
            const dataWithSerialNo = resData.data.map((item, index) => ({
              ...item,
              serialNo: (pagination.current - 1) * pagination.pageSize + index + 1,
            }));
            return {
              data: dataWithSerialNo,
              total: resData.total,
              success: true,
            };
          }
          message.error('请求数据出错，请刷新重试或联系管理员');
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={220}
        rowOperation={(row) => {
          return [
            {
              element: (
                <div>
                  <Space size="small">
                    <Button size="small" type="link" onClick={() => {}}>
                      预览
                    </Button>

                    <Button size="small" type="link" onClick={() => {}}>
                      编辑
                    </Button>

                    <Button size="small" type="link" onClick={() => {}}>
                      下载
                    </Button>

                    <Button
                      size="small"
                      type="link"
                      danger
                      onClick={() => {
                        YTHDialog.show({
                          type: 'confirm',
                          content: <p>确认删除此条数据？</p>,
                          onCancle: () => {},
                          onConfirm: () => {
                            confirmDelete(row);
                          },
                          p_props: {
                            cancelText: '取消',
                            okText: '确定',
                            title: '删除',
                          },
                        });
                      }}
                    >
                      删除
                    </Button>
                  </Space>
                </div>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        width="50%"
        title={modalTypeData[modalType]}
        footer={null}
        visible={visiable}
        onCancel={() => setVisiable(false)}
        destroyOnClose
        maskClosable={false}
      >
        <YTHForm form={form} col={2}>
          <YTHForm.Item
            name="id"
            title="id"
            labelType={1}
            required={false}
            display="hidden"
            componentName="Input"
            componentProps={{
              disabled: true,
            }}
          />
          <YTHForm.Item
            name="reportTime"
            title="月报时间"
            labelType={1}
            required
            componentName="DatePicker"
            componentProps={{
              precision: 'month',
              formatter: 'YYYY-MM',
            }}
          />
          <YTHForm.Item
            name="reportName"
            title="报告名称"
            labelType={1}
            required
            componentName="Input"
            componentProps={{}}
          />
        </YTHForm>
        <div className={style['modal-footer']}>
          <Button onClick={() => setVisiable(false)}>取消</Button>
          {(modalType === 'add' || modalType === 'edit') && (
            <Button
              type="primary"
              onClick={() => {
                formSave();
              }}
              style={{ marginLeft: 20 }}
            >
              保存
            </Button>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(ReportList, locales, YTHLocalization.getLanguage());
