import React, { useEffect, useRef, useState } from 'react';
import { Spin } from 'antd';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import moment from 'moment';

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LineChart,
  CanvasRenderer,
  LegendComponent,
  UniversalTransition,
]);

/**
 * @description 空气质量数据类型
 */
type AirQualityDataType = {
  date: string;
  ozone: number; // 臭氧
  carbonMonoxide: number; // 一氧化碳
  sulfurDioxide: number; // 二氧化硫
  nitrogenDioxide: number; // 二氧化氮
  PM10: number; // PM10
  PM25: number; // PM2.5
};

/**
 * @description 指标键类型
 */
type IndicatorKey = keyof Omit<AirQualityDataType, 'date'>;

/**
 * @description 组件属性类型
 */
type PropsType = {
  /** 选中的月份 */
  selectedMonth: moment.Moment;
  /** 加载状态 */
  loading?: boolean;
  /** 空气质量数据 */
  airQualityData?: any[];
  /** 时间范围 */
  dateRange?: [moment.Moment | null, moment.Moment | null];
};

/**
 * @description 空气质量折线图组件
 * @param props 组件属性
 * @returns React组件
 */
const AirQualityChart: React.FC<PropsType> = (props) => {
  const { selectedMonth, loading = false, airQualityData = [], dateRange } = props;
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const [chartData, setChartData] = useState<AirQualityDataType[]>([]);

  // 六个空气质量指标配置
  const indicators: Array<{ key: IndicatorKey; name: string; unit: string; color: string }> = [
    { key: 'ozone', name: '臭氧', unit: 'μg/m³', color: '#FF6B6B' },
    { key: 'carbonMonoxide', name: '一氧化碳', unit: 'μg/m³', color: '#4ECDC4' },
    { key: 'sulfurDioxide', name: '二氧化硫', unit: 'μg/m³', color: '#45B7D1' },
    { key: 'nitrogenDioxide', name: '二氧化氮', unit: 'μg/m³', color: '#96CEB4' },
    { key: 'PM10', name: 'PM10', unit: 'μg/m³', color: '#FFEAA7' },
    { key: 'PM25', name: 'PM2.5', unit: 'μg/m³', color: '#DDA0DD' },
  ];

  // 生成模拟数据
  const generateMockData = (month: moment.Moment): AirQualityDataType[] => {
    const data: AirQualityDataType[] = [];
    const daysInMonth = month.daysInMonth();

    for (let day = 1; day <= daysInMonth; day += 1) {
      const date = month.clone().date(day).format('MM-DD');

      data.push({
        date,
        ozone: Math.round(80 + Math.random() * 60), // 80-140
        carbonMonoxide: Math.round(60 + Math.random() * 80), // 60-140
        sulfurDioxide: Math.round(40 + Math.random() * 60), // 40-100
        nitrogenDioxide: Math.round(50 + Math.random() * 70), // 50-120
        PM10: Math.round(70 + Math.random() * 80), // 70-150
        PM25: Math.round(30 + Math.random() * 50), // 30-80
      });
    }

    return data;
  };

  // 格式化数值
  const formatValue = (value: number) => {
    return Math.round(value * 100) / 100;
  };

  // 图表配置
  const getChartOption = () => {
    const series = indicators.map((indicator) => ({
      name: indicator.name,
      type: 'line',
      data: chartData.map((item) => item[indicator.key]),
      smooth: true,
      symbol: 'circle',
      symbolSize: 4,
      lineStyle: {
        width: 2,
        color: indicator.color,
      },
      itemStyle: {
        color: indicator.color,
      },
    }));

    return {
      tooltip: {
        trigger: 'axis',
        formatter: (params: unknown) => {
          const paramArray = params as Array<{
            name: string;
            marker: string;
            seriesName: string;
            value: number;
          }>;
          let result = `${paramArray[0].name}<br/>`;
          paramArray.forEach((param) => {
            const indicator = indicators.find((ind) => ind.name === param.seriesName);
            result += `${param.marker} ${param.seriesName}: ${formatValue(param.value)} ${indicator?.unit || ''}<br/>`;
          });
          return result;
        },
      },
      legend: {
        data: indicators.map((ind) => ind.name),
        top: 10,
        textStyle: {
          fontSize: 12,
        },
      },
      grid: {
        left: '2%',
        right: '2%',
        bottom: '12%',
        top: '12%',
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {
            show: true,
            title: '保存为图片',
          },
        },
        right: '2%',
        top: '0%',
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.map((item) => item.date),
        axisLabel: {
          fontSize: 10,
          rotate: 0,
        },
        axisTick: {
          alignWithLabel: true,
        },
      },
      yAxis: {
        type: 'value',
        // name: '浓度(μg/m³)',
        nameTextStyle: {
          fontSize: 12,
        },
        axisLabel: {
          formatter: (value: number) => formatValue(value),
          fontSize: 10,
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#f0f0f0',
          },
        },
      },
      dataZoom: [
        {
          type: 'slider',
          start: 0,
          end: 100,
          height: 15,
          bottom: '3%',
          textStyle: {
            fontSize: 10,
          },
        },
        {
          type: 'inside',
          start: 0,
          end: 100,
        },
      ],
      series,
    };
  };

  // 初始化图表
  useEffect(() => {
    if (chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
      // 初始化后立即调用 resize 确保图表占满容器
      setTimeout(() => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      }, 100);
    }
  }, []);

  // 处理传入的真实数据
  const processRealData = (data: any[]): AirQualityDataType[] => {
    if (!data || data.length === 0) {
      // 如果没有真实数据，使用模拟数据
      return generateMockData(selectedMonth);
    }

    // 处理真实数据，转换为图表需要的格式
    return data.map((item) => ({
      date: moment(item.time || item.date).format('MM-DD'),
      ozone: item.ozone || 0,
      carbonMonoxide: item.carbonMonoxide || 0,
      sulfurDioxide: item.sulfurDioxide || 0,
      nitrogenDioxide: item.nitrogenDioxide || 0,
      PM10: item.PM10 || 0,
      PM25: item.PM25 || item['PM2.5'] || 0,
    }));
  };

  // 当数据变化时更新图表
  useEffect(() => {
    if (airQualityData && airQualityData.length > 0) {
      // 使用真实数据
      const processedData = processRealData(airQualityData);
      setChartData(processedData);
    } else if (selectedMonth) {
      // 使用模拟数据
      const mockData = generateMockData(selectedMonth);
      setChartData(mockData);
    }
  }, [airQualityData, selectedMonth]);

  // 更新图表数据
  useEffect(() => {
    if (chartInstance.current && chartData.length > 0) {
      const option = getChartOption();
      chartInstance.current.setOption(option, true);
      // 设置配置后调用 resize 确保图表正确显示
      setTimeout(() => {
        if (chartInstance.current) {
          chartInstance.current.resize();
        }
      }, 50);
    }
  }, [chartData]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 清理图表实例
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  return (
    <Spin spinning={loading}>
      <div
        ref={chartRef}
        style={{
          width: '100%',
          height: '100%',
          minHeight: '280px',
          maxHeight: '300px',
        }}
      />
    </Spin>
  );
};

export default AirQualityChart;
