import { envRequest } from '@/request';

/**
 * 数据分析模块api
 */
const analysisApi = {
  /** 删除分析报告 */
  deleteReport: (id: string) => {
    return envRequest.post('/envQualityOnLineMonitor/deleteReport', { id });
  },
  /** 查询分析报告 */
  queryReport: (id: string) => {
    return envRequest.post('/envQualityOnLineMonitor/queryReport', { id });
  },
  /** 新增分析报告 */
  addReport: (data: Record<string, string | number>) => {
    return envRequest.post('/envQualityOnLineMonitor/addReport', { data });
  },
  /** 查询空气监测信息 */
  queryAirMonitorInfo: (data: Record<string, string | number>) => {
    return envRequest.post('/staticAnalysis/queryAirMonitorInfo', { data });
  },
  /** 根据月份查询监测信息 */
  queryMonitorInfoByMonth: (data: Record<string, string | number>) => {
    return envRequest.post('/staticAnalysis/queryMonitorInfoByMonth', { data });
  },
  /** 查询监测指标信息 */
  queryMonitorIndexInfo: (data: Record<string, string | number>) => {
    return envRequest.post('/staticAnalysis/queryMonitorIndexInfo', { data });
  },
};

export default analysisApi;
