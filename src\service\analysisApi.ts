import { envRequest } from '@/request';

/**
 * 数据分析模块api
 */
const analysisApi = {
  /** 查询空气监测信息 */
  queryAirMonitorInfo: (data: Record<string, string | number>) => {
    return envRequest.post('/staticAnalysis/queryAirMonitorInfo', { data });
  },
  /** 根据月份查询监测信息 */
  queryMonitorInfoByMonth: (data: Record<string, string | number>) => {
    return envRequest.post('/staticAnalysis/queryMonitorInfoByMonth', { data });
  },
  /** 查询监测指标信息 */
  queryMonitorIndexInfo: (data: Record<string, string | number>) => {
    return envRequest.post('/staticAnalysis/queryMonitorIndexInfo', { data });
  },
  /** 查询监测指标统计图信息 */
  queryMonitorIndexChartInfo: (data: Record<string, string | number>) => {
    return envRequest.post('/staticAnalysis/queryMonitorIndexChartInfo', { data });
  },
  /** 生成分析报告 */
  generateAnalysisReport: (data: Record<string, string | number>) => {
    return envRequest.post('/statisticsReport/createAnalysisReports', { data });
  },
  /** 下载分析报告 */
  downloadAnalysisReport: (data: Record<string, string | number>) => {
    return envRequest.post('/envQualityOnLineMonitor/downloadAnalysisReport', { data });
  },
  /** 查询分析报告列表 */
  queryAnalysisReportList: (data) => {
    return envRequest.post('/envQualityOnLineMonitor/queryAnalysisReportList', { data });
  },
  /** 删除分析报告 */
  deleteReport: (id: string) => {
    return envRequest.delete(`/envQualityOnLineMonitor/deleteAnalysisReport?id=${id}`);
  },
};

export default analysisApi;
