import moment from 'moment';

/** 通用对象类型 */
export type ObjectType = Record<string, string>;

/** 日期选择器类型 */
export type PickerType = 'date' | 'week' | 'month' | 'quarter' | 'year' | 'dateTime';

/** 时间列表类型 */
export type DateListTypes = {
  text: string;
  code?: string;
  dataCode?: string;
  key: string;
  format: string;
  over?: number; // 超过多少天不允许查询
  pickerType: PickerType; // 日期选择器类型
  defaultRange: () => [moment.Moment, moment.Moment] | moment.Moment; // 默认时间范围
};

/** 监测类型配置 */
export type MonitorTypeConfig = {
  /** 字典键值 */
  monitorKey: string;
  /** 监测类型值 */
  onLineMonitorType: string;
  /** 导出报表名称 */
  exportName: string;
  /** 是否过滤监测类型列表 */
  filterMonitorList?: boolean;
  /** 过滤监测类型的条件 */
  filterCondition?: (item: ObjectType) => boolean;
};

/** 在线监测组件Props */
export type OnlineMonitorProps = {
  /** 监测类型 */
  monitorConfigType: 'envQuality' | 'wasteGas' | 'utilityMonitor' | 'gasLeak';
  /** 自定义配置，用于覆盖默认配置 */
  customConfig?: Partial<MonitorTypeConfig>;
};

/** 在线监测标签类型 */
export type OnlineMonitorTagType = {
  text: string;
  key: string;
  color: string;
  borderColor?: string;
};
