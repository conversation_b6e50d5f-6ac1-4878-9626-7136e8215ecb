import React, { useState, useEffect } from 'react';
import { Modal, DatePicker, Space, Button, Spin, message } from 'antd';
import moment from 'moment';
import analysisApi from '@/service/analysisApi';

/**
 * @description 图表数据类型
 */
type ChartDataType = {
  time: string;
  value: number;
};

/**
 * @description 组件属性类型
 */
type PropsType = {
  /** 是否显示弹窗 */
  visible: boolean;
  /** 关闭弹窗回调 */
  onClose: () => void;
  /** 设备ID */
  equipId: string;
  /** 监测指标信息 */
  monitorInfo: {
    code: string;
    name: string;
    measureUnit: string;
  };
};

/**
 * @description 监测数据折线图组件
 * @param props 组件属性
 * @returns React组件
 */
const MonitorDataChart: React.FC<PropsType> = (props) => {
  const { visible, onClose, equipId, monitorInfo } = props;

  // 状态管理
  const [chartData, setChartData] = useState<ChartDataType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 默认时间：今天开始到现在
  const [startTime, setStartTime] = useState<moment.Moment>(moment().startOf('day'));
  const [endTime, setEndTime] = useState<moment.Moment>(moment());

  // 验证时间范围（最多7天）
  const validateTimeRange = () => {
    const duration = endTime.diff(startTime, 'days');
    if (duration > 7) {
      message.warning('查询时间范围不能超过7天');
      return false;
    }
    if (startTime.isAfter(endTime)) {
      message.warning('开始时间不能晚于结束时间');
      return false;
    }
    return true;
  };

  // 模拟API查询数据
  const queryChartData = async () => {
		// queryMonitorIndexChartInfo
    if (!equipId || !monitorInfo.code) {
      message.warning('缺少设备ID或指标代码');
      return;
    }

    // 验证时间范围
    if (!validateTimeRange()) {
      return;
    }

    try {
      setLoading(true);

      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 生成模拟数据
      const mockData: ChartDataType[] = [];
      const start = startTime.clone();
      const end = endTime.clone();
      const duration = end.diff(start, 'minutes');

      // 根据时间范围决定数据点间隔
      let interval = 60; // 默认1小时间隔
      if (duration <= 60) {
        interval = 5; // 1小时内，5分钟间隔
      } else if (duration <= 1440) {
        interval = 30; // 1天内，30分钟间隔
      } else if (duration <= 10080) {
        interval = 120; // 1周内，2小时间隔
      }

      // 生成数据点
      for (let i = 0; i <= duration; i += interval) {
        const time = start.clone().add(i, 'minutes');
        if (time.isAfter(end)) break;

        // 基于指标类型生成不同范围的随机数据
        let baseValue = 50;
        let variation = 20;

        // 根据指标代码调整数据范围
        switch (monitorInfo.code.toLowerCase()) {
          case 'o3':
          case 'ozone':
            baseValue = 80;
            variation = 30;
            break;
          case 'co':
          case 'carbonmonoxide':
            baseValue = 1.2;
            variation = 0.5;
            break;
          case 'so2':
          case 'sulfurdioxide':
            baseValue = 15;
            variation = 8;
            break;
          case 'no2':
          case 'nitrogendioxide':
            baseValue = 25;
            variation = 10;
            break;
          case 'pm10':
            baseValue = 60;
            variation = 25;
            break;
          case 'pm25':
          case 'pm2.5':
            baseValue = 35;
            variation = 15;
            break;
          default:
            baseValue = 50;
            variation = 20;
        }

        // 生成带有趋势的随机数据
        const trend = Math.sin((i / duration) * Math.PI * 2) * (variation * 0.3);
        const randomVariation = (Math.random() - 0.5) * variation;
        const value = Math.max(0, baseValue + trend + randomVariation);

        mockData.push({
          time: time.format('YYYY-MM-DD HH:mm'),
          value: Number(value.toFixed(2)),
        });
      }

      console.log('生成的图表数据:', mockData);
      setChartData(mockData);

      if (mockData.length === 0) {
        message.info('查询时间范围内暂无数据');
      }
    } catch (error) {
      console.error('查询数据失败:', error);
      message.error('查询失败，请稍后重试');
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };

  // 重置时间为今天开始到现在
  const resetTimeRange = () => {
    setStartTime(moment().startOf('day'));
    setEndTime(moment());
  };

  // 禁用开始时间的日期（不能选择7天前之前的日期）
  const disabledStartDate = (current: moment.Moment) => {
    if (!current) return false;
    // 不能选择未来日期
    if (current.isAfter(moment(), 'day')) return true;
    // 如果已选择结束时间，开始时间不能早于结束时间7天前
    if (endTime) {
      return current.isBefore(endTime.clone().subtract(7, 'days'), 'day');
    }
    // 不能选择7天前之前的日期
    return current.isBefore(moment().subtract(7, 'days'), 'day');
  };

  // 禁用结束时间的日期
  const disabledEndDate = (current: moment.Moment) => {
    if (!current) return false;
    // 不能选择未来日期
    if (current.isAfter(moment(), 'day')) return true;
    // 如果已选择开始时间，结束时间不能晚于开始时间7天后
    if (startTime) {
      return current.isAfter(startTime.clone().add(7, 'days'), 'day');
    }
    return false;
  };

  // 弹窗打开时自动查询数据
  useEffect(() => {
    if (visible) {
      queryChartData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // 渲染简单的数据表格
  const renderDataTable = () => {
    if (chartData.length === 0) {
      return (
        <div
          style={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#999',
            fontSize: '14px',
          }}
        >
          暂无数据，请调整时间范围后重新查询
        </div>
      );
    }

    return (
      <div style={{ maxHeight: 350, overflowY: 'auto' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#fafafa' }}>
              <th style={{ padding: '8px', border: '1px solid #d9d9d9', textAlign: 'left' }}>
                时间
              </th>
              <th style={{ padding: '8px', border: '1px solid #d9d9d9', textAlign: 'left' }}>
                {monitorInfo.name} ({monitorInfo.measureUnit})
              </th>
            </tr>
          </thead>
          <tbody>
            {chartData.map((item) => (
              <tr key={item.time}>
                <td style={{ padding: '8px', border: '1px solid #d9d9d9' }}>{item.time}</td>
                <td style={{ padding: '8px', border: '1px solid #d9d9d9' }}>{item.value}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <Modal
      title={`${monitorInfo.name}数据趋势图`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
      centered
    >
      {/* 时间选择和查询区域 */}
      <div style={{ marginBottom: 16 }}>
        <Space wrap>
          <span>开始时间：</span>
          <DatePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            value={startTime}
            onChange={(value) => setStartTime(value || moment().startOf('day'))}
            placeholder="选择开始时间"
            disabledDate={disabledStartDate}
          />
          <span>结束时间：</span>
          <DatePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            value={endTime}
            onChange={(value) => setEndTime(value || moment())}
            placeholder="选择结束时间"
            disabledDate={disabledEndDate}
          />
          <Button type="primary" onClick={queryChartData} loading={loading}>
            查询
          </Button>
          <Button onClick={resetTimeRange}>重置</Button>
        </Space>
      </div>

      {/* 数据显示区域 */}
      <Spin spinning={loading}>
        <div style={{ height: 400, padding: '16px 0' }}>{renderDataTable()}</div>
      </Spin>

      {/* 查询信息显示 */}
      <div
        style={{
          marginTop: 16,
          padding: 12,
          backgroundColor: '#f5f5f5',
          borderRadius: 4,
          fontSize: '12px',
          color: '#666',
        }}
      >
        <div>
          <strong>监测指标:</strong> {monitorInfo.name} ({monitorInfo.code})
        </div>
        <div>
          <strong>测量单位:</strong> {monitorInfo.measureUnit}
        </div>
        <div>
          <strong>设备ID:</strong> {equipId}
        </div>
        <div>
          <strong>数据点数:</strong> {chartData.length}
        </div>
        <div>
          <strong>查询时间:</strong> {startTime.format('YYYY-MM-DD HH:mm:ss')} ~{' '}
          {endTime.format('YYYY-MM-DD HH:mm:ss')}
        </div>
        <div style={{ color: '#999', marginTop: 4 }}>
          <strong>提示:</strong> 查询时间范围最多支持7天
        </div>
      </div>
    </Modal>
  );
};

export default MonitorDataChart;
