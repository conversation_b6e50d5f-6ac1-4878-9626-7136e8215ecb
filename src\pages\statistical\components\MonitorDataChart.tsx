import React, { useEffect, useRef, useState } from 'react';
import { Modal, Spin, DatePicker, Space, Button, message } from 'antd';
import moment from 'moment';
import analysisApi from '@/service/analysisApi';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LineChart,
  CanvasRenderer,
  LegendComponent,
  UniversalTransition,
]);

/**
 * @description 监测数据类型
 */
type MonitorDataType = {
  time: string;
  value: number;
  status?: string; // 数据状态标识
};

/**
 * @description 组件属性类型
 */
type PropsType = {
  /** 是否显示弹窗 */
  visible: boolean;
  /** 关闭弹窗回调 */
  onClose: () => void;
  /** 设备ID */
  equipId: string;
  /** 监测指标信息 */
  monitorInfo: {
    code: string;
    name: string;
    measureUnit: string;
  };
};

/**
 * @description 监测数据折线图组件
 * @param props 组件属性
 * @returns React组件
 */
const MonitorDataChart: React.FC<PropsType> = (props) => {
  const { visible, onClose, equipId, monitorInfo } = props;
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 添加调试信息
  console.log('MonitorDataChart props:', { visible, equipId, monitorInfo });

  // 内部状态管理
  const [chartData, setChartData] = useState<MonitorDataType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 默认时间：今天开始到现在
  const [startTime, setStartTime] = useState<moment.Moment>(moment().startOf('day'));
  const [endTime, setEndTime] = useState<moment.Moment>(moment());

  // 查询监测数据
  const queryChartData = async () => {
    if (!equipId || !monitorInfo.code) {
      message.warning('缺少设备ID或指标代码');
      return;
    }

    try {
      setLoading(true);

      // 构建查询参数
      const params = {
        equipId,
        code: monitorInfo.code,
        startTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
        endTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
      };

      console.log('查询参数:', params);

      // 调用API获取历史数据
      // 注意：这里需要根据实际的API接口调整
      const response = await analysisApi.queryMonitorIndexInfo(params);

      if (response.code === 200 && Array.isArray(response.data)) {
        // 处理返回的数据
        const formattedData: MonitorDataType[] = response.data.map((item: any) => ({
          time: moment(item.monitorTime || item.time).format('MM-DD HH:mm'),
          value: parseFloat(item.number || item.value || '0'),
          status: item.status || 'normal',
        }));

        console.log('查询到的数据:', formattedData);
        setChartData(formattedData);

        if (formattedData.length === 0) {
          message.info('查询时间范围内暂无数据');
        }
      } else {
        console.log('API返回数据格式异常:', response);
        setChartData([]);
        message.warning('查询失败，请稍后重试');
      }
    } catch (error) {
      console.error('查询监测数据失败:', error);
      setChartData([]);
      message.error('查询失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 重置时间为今天开始到现在
  const resetTimeRange = () => {
    setStartTime(moment().startOf('day'));
    setEndTime(moment());
  };

  // 格式化数值
  const formatValue = (value: number) => {
    return value ? Math.round(value * 1000) / 1000 : 0;
  };

  // 图表配置
  const getChartOption = () => {
    return {
      title: {
        text: `${monitorInfo.name}趋势图`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const param = params[0];
          return `
            ${param.name}<br/>
            ${param.marker} ${monitorInfo.name}: ${formatValue(param.value)} ${monitorInfo.measureUnit}
          `;
        },
      },
      legend: {
        data: [monitorInfo.name],
        top: 30,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {
            show: true,
            title: '保存为图片',
          },
          dataZoom: {
            show: true,
            title: {
              zoom: '区域缩放',
              back: '区域缩放还原',
            },
          },
        },
        right: '2%',
        top: '8%',
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.map((item) => item.time),
        axisLabel: {
          rotate: 45,
          fontSize: 10,
        },
      },
      yAxis: {
        type: 'value',
        name: `${monitorInfo.name}(${monitorInfo.measureUnit})`,
        nameTextStyle: {
          fontSize: 12,
        },
        axisLabel: {
          formatter: (value: number) => formatValue(value),
        },
      },
      dataZoom: [
        {
          type: 'slider',
          start: 0,
          end: 100,
          height: 20,
          bottom: '5%',
        },
        {
          type: 'inside',
          start: 0,
          end: 100,
        },
      ],
      series: [
        {
          name: monitorInfo.name,
          type: 'line',
          data: chartData.map((item) => item.value),
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            width: 2,
            color: '#1890ff',
          },
          itemStyle: {
            color: '#1890ff',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(24, 144, 255, 0.3)',
                },
                {
                  offset: 1,
                  color: 'rgba(24, 144, 255, 0.1)',
                },
              ],
            },
          },
        },
      ],
    };
  };

  // 初始化图表
  useEffect(() => {
    if (visible && chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }
  }, [visible]);

  // 弹窗打开时自动查询数据
  useEffect(() => {
    if (visible) {
      queryChartData();
    }
  }, [visible]);

  // 更新图表数据
  useEffect(() => {
    if (visible && chartRef.current) {
      // 确保图表实例存在
      if (!chartInstance.current) {
        chartInstance.current = echarts.init(chartRef.current);
      }

      // 如果有数据则更新图表
      if (chartData.length > 0) {
        const option = getChartOption();
        chartInstance.current.setOption(option, true);
      } else {
        // 清空图表
        chartInstance.current.clear();
      }
    }
  }, [chartData, visible, monitorInfo]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    if (visible) {
      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }

    return undefined;
  }, [visible]);

  // 清理图表实例
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  return (
    <Modal
      title={`${monitorInfo.name}数据趋势`}
      visible={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
      centered
    >
      <div style={{ marginBottom: 16 }}>
        <Space>
          <span>开始时间：</span>
          <DatePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            value={startTime}
            onChange={(value) => setStartTime(value || moment().startOf('day'))}
            placeholder="选择开始时间"
          />
          <span>结束时间：</span>
          <DatePicker
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            value={endTime}
            onChange={(value) => setEndTime(value || moment())}
            placeholder="选择结束时间"
          />
          <Button type="primary" onClick={queryChartData} loading={loading}>
            查询
          </Button>
          <Button onClick={resetTimeRange}>
            重置为今天
          </Button>
        </Space>
      </div>
      <Spin spinning={loading}>
        <div
          ref={chartRef}
          style={{
            width: '100%',
            height: '500px',
            minHeight: '400px',
            border: '1px solid #d9d9d9', // 临时边框，用于调试
          }}
        >
          {/* 临时显示数据状态 */}
          {!loading && chartData.length === 0 && (
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                color: '#999',
              }}
            >
              暂无数据
            </div>
          )}
        </div>
      </Spin>
    </Modal>
  );
};

export default MonitorDataChart;
