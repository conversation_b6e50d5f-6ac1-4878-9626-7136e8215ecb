import React, { useEffect, useRef } from 'react';
import { Modal, Spin } from 'antd';
import * as echarts from 'echarts/core';
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LegendComponent,
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册 ECharts 组件
echarts.use([
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  DataZoomComponent,
  LineChart,
  CanvasRenderer,
  LegendComponent,
  UniversalTransition,
]);

/**
 * @description 监测数据类型
 */
type MonitorDataType = {
  time: string;
  value: number;
  status?: string; // 数据状态标识
};

/**
 * @description 组件属性类型
 */
type PropsType = {
  /** 是否显示弹窗 */
  visible: boolean;
  /** 关闭弹窗回调 */
  onClose: () => void;
  /** 监测指标信息 */
  monitorInfo: {
    code: string;
    name: string;
    measureUnit: string;
  };
  /** 图表数据 */
  chartData: MonitorDataType[];
  /** 加载状态 */
  loading?: boolean;
};

/**
 * @description 监测数据折线图组件
 * @param props 组件属性
 * @returns React组件
 */
const MonitorDataChart: React.FC<PropsType> = (props) => {
  const { visible, onClose, monitorInfo, chartData, loading = false } = props;
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  // 格式化数值
  const formatValue = (value: number) => {
    return value ? Math.round(value * 1000) / 1000 : 0;
  };

  // 图表配置
  const getChartOption = () => {
    return {
      title: {
        text: `${monitorInfo.name}趋势图`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold',
        },
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const param = params[0];
          return `
            ${param.name}<br/>
            ${param.marker} ${monitorInfo.name}: ${formatValue(param.value)} ${monitorInfo.measureUnit}
          `;
        },
      },
      legend: {
        data: [monitorInfo.name],
        top: 30,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
        top: '15%',
        containLabel: true,
      },
      toolbox: {
        feature: {
          saveAsImage: {
            show: true,
            title: '保存为图片',
          },
          dataZoom: {
            show: true,
            title: {
              zoom: '区域缩放',
              back: '区域缩放还原',
            },
          },
        },
        right: '2%',
        top: '8%',
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.map((item) => item.time),
        axisLabel: {
          rotate: 45,
          fontSize: 10,
        },
      },
      yAxis: {
        type: 'value',
        name: `${monitorInfo.name}(${monitorInfo.measureUnit})`,
        nameTextStyle: {
          fontSize: 12,
        },
        axisLabel: {
          formatter: (value: number) => formatValue(value),
        },
      },
      dataZoom: [
        {
          type: 'slider',
          start: 0,
          end: 100,
          height: 20,
          bottom: '5%',
        },
        {
          type: 'inside',
          start: 0,
          end: 100,
        },
      ],
      series: [
        {
          name: monitorInfo.name,
          type: 'line',
          data: chartData.map((item) => item.value),
          smooth: true,
          symbol: 'circle',
          symbolSize: 4,
          lineStyle: {
            width: 2,
            color: '#1890ff',
          },
          itemStyle: {
            color: '#1890ff',
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(24, 144, 255, 0.3)',
                },
                {
                  offset: 1,
                  color: 'rgba(24, 144, 255, 0.1)',
                },
              ],
            },
          },
        },
      ],
    };
  };

  // 初始化图表
  useEffect(() => {
    if (visible && chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }
  }, [visible]);

  // 更新图表数据
  useEffect(() => {
    if (chartInstance.current && visible && chartData.length > 0) {
      const option = getChartOption();
      chartInstance.current.setOption(option, true);
    }
  }, [chartData, visible, monitorInfo]);

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    };

    if (visible) {
      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [visible]);

  // 清理图表实例
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  return (
    <Modal
      title={`${monitorInfo.name}数据趋势`}
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      destroyOnClose
      centered
    >
      <Spin spinning={loading}>
        <div
          ref={chartRef}
          style={{
            width: '100%',
            height: '500px',
            minHeight: '400px',
          }}
        />
      </Spin>
    </Modal>
  );
};

export default MonitorDataChart;
