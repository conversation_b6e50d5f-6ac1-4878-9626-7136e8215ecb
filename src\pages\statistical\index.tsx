import React, { useState, useEffect } from 'react';
import { Spin, Tabs, DatePicker, Select, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import moment from 'moment';
import analysisApi from '@/service/analysisApi';
import MonitorDataChart from './components/MonitorDataChart';
import AirQualityChart from './components/AirQualityChart';
import style from './index.module.less';

const { RangePicker } = DatePicker;

/**
 * @description 空气质量数据类型
 */
type AirQualityType = {
  airNum?: number; // AQI指数
  aqiLevel?: string; // AQI等级
  complianceDays?: number; // 合格天数
  monitorDays?: number; // 监测天数
  passRate?: number; // 合格率
  airLeavel?: number; // 空气质量等级
};

/**
 * @description 监测指标信息类型
 */
type MonitorIndexInfoType = {
  code: string;
  measureUnit: string;
  name: string;
  number: number;
};

/**
 * @description 空气质量表格数据类型
 */
type AirQualityTableDataType = {
  airLeavel: number;
  airLevelText: string | null;
  airNum: string;
  monitorDays: number;
  complianceDays: number;
  passRate: number;
  monitorTime: string;
  pm25Num: string;
  pm10Num: string;
  so2Num: string;
  no2Num: string;
  coNum: string;
  o3Num: string;
  fluoride: string;
  primaryPollutant: string;
};

/**
 * @description 空气质量等级数据类型
 */
type LevelDataType = {
  level: string;
  color: string;
  min: number;
  max: number;
  limit?: number;
  key: number;
};

type History = {
  push: (path: string) => void;
};

/**
 * @description 空气质量等级数据
 */
const levelData: LevelDataType[] = [
  {
    level: '优',
    color: '#00FF00',
    min: 0,
    max: 50,
    key: 1,
  },
  {
    level: '良',
    color: '#FFFF00',
    min: 51,
    max: 100,
    key: 2,
  },
  {
    level: '轻度污染',
    color: '#FFA500',
    min: 101,
    max: 150,
    key: 3,
  },
  {
    level: '中度污染',
    color: '#FF0000',
    min: 151,
    max: 200,
    key: 4,
  },
  {
    level: '重度污染',
    color: '#9a014d',
    min: 201,
    max: 300,
    key: 5,
  },
  {
    level: '严重污染',
    color: '#7d0124',
    min: 301,
    limit: 300,
    max: 999,
    key: 6,
  },
];

/**
 * @description 主要监测指标
 */
const mainMonitor: { label: string; value: string }[] = [
  {
    label: '空气质量监测指标',
    value: 'air',
  },
  {
    label: '土壤监测指标',
    value: 'soil',
  },
  {
    label: '地下水监测指标',
    value: 'groundwater',
  },
  {
    label: '地表水监测指标',
    value: 'surfaceWater',
  },
];

/**
 * @description 根据AQI值获取指示器位置百分比
 * @param aqi AQI值
 * @returns 位置百分比
 */
const getAqiPosition = (aqi: number): number => {
  // 限制在0-300范围内
  const limitedAqi = Math.min(Math.max(aqi || 0, 0), 300);
  return (limitedAqi / 300) * 100;
};

/**
 * @description 根据AQI值获取空气质量等级
 * @returns 空气质量等级
 */
const getAqiLevel = (key: number): string => {
  // 使用find方法代替for循环
  const found = levelData.find((item) => item.key === key);
  return found ? found.level : '未知';
};

/**
 * @description 统计分析首页
 * @returns {React.ReactElement} 统计分析首页
 */
const StatisticalIndex: React.FC<{ history: History }> = ({ history }) => {
  const [airQuality, setAirQuality] = useState<AirQualityType>({}); // 空气质量数据
  const [mainMonitorKey, setMainMonitorKey] = useState<string>('air'); // 主要监测指标key
  const [updateTime, setUpdateTime] = useState<string>(''); // 数据更新时间
  const [loading, setLoading] = useState<boolean>(false); // 通用加载状态
  const [airMonitorLoading, setAirMonitorLoading] = useState<boolean>(false); // 空气监测信息加载状态
  const [monthMonitorLoading, setMonthMonitorLoading] = useState<boolean>(false); // 月度监测信息加载状态
  const [mainMonitorData, setMainMonitorData] = useState<MonitorIndexInfoType[]>([]); // 主要监测指标数据
  const [month, setMonth] = useState<moment.Moment>(moment()); // 月份
  const [rangeDate, setRangeDate] = useState<[moment.Moment | null, moment.Moment | null]>([
    moment().subtract(1, 'month'),
    moment(),
  ]); // 时间范围

  // Modal 相关状态
  const [chartModalVisible, setChartModalVisible] = useState<boolean>(false); // 图表弹窗显示状态
  const [selectedMonitorItem, setSelectedMonitorItem] = useState<MonitorIndexInfoType | null>(null); // 选中的监测指标
  const [chartData, setChartData] = useState<Array<{ time: string; value: number }>>([]); // 图表数据
  const [chartLoading, setChartLoading] = useState<boolean>(false); // 图表加载状态

  // 空气质量图表数据
  const [airQualityData, setAirQualityData] = useState<AirQualityTableDataType[]>([]); // 空气质量监测数据

  // 获取主要监测指标数据
  const getMonitorData = () => {
    setLoading(true);
    setTimeout(() => {
      setUpdateTime(dayjs().format('YYYY-MM-DD HH:mm:ss'));
      setLoading(false);
      setMainMonitorData([
        { code: 'ozone', name: '臭氧', number: 120, measureUnit: 'μg/m³' },
        { code: 'carbonMonoxide', name: '一氧化碳', number: 100, measureUnit: 'μg/m³' },
        { code: 'sulfurDioxide', name: '二氧化硫', number: 120, measureUnit: 'μg/m³' },
        { code: 'nitrogenDioxide', name: '二氧化氮', number: 120, measureUnit: 'μg/m³' },
        { code: 'PM10', name: 'PM10', number: 120, measureUnit: 'μg/m³' },
        { code: 'PM2.5', name: 'PM2.5', number: 120, measureUnit: 'μg/m³' },
        { code: 'fluorine', name: '氟化物', number: 120, measureUnit: 'μg/m³' },
        { code: 'temperature', name: '温度', number: 120, measureUnit: '℃' },
        { code: 'humidity', name: '湿度', number: 120, measureUnit: '%' },
        { code: 'pressure', name: '气压', number: 120, measureUnit: 'hPa' },
      ]);
    }, 1000);
    // queryUnitInfoByType('company')
    //   .then((res) => {
    //     setUpdateTime(dayjs().format('YYYY-MM-DD HH:mm:ss'));
    //   })
    //   .finally(() => {
    //     setLoading(false);
    //   });
  };

  // 切换主要监测指标
  const onChange = (key: string) => {
    setMainMonitorKey(key);
  };
  // 设备切换
  const onEquipChange = (e) => {
    console.log('e', e);
  };

  // 处理监测指标点击事件
  const handleMonitorItemClick = (item: MonitorIndexInfoType) => {
    setSelectedMonitorItem(item);
    setChartModalVisible(true);
    fetchChartData(item);
  };

  // 获取图表数据（模拟数据）
  const fetchChartData = async (item: MonitorIndexInfoType) => {
    setChartLoading(true);
    try {
      // 模拟API调用延迟
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 生成模拟数据 - 最近24小时的数据
      const mockData = [];
      const now = moment();
      for (let i = 23; i >= 0; i--) {
        const time = now.clone().subtract(i, 'hours').format('MM-DD HH:mm');
        // 基于基础值生成随机波动数据
        const baseValue = item.number;
        const variation = baseValue * 0.2; // 20%的波动范围
        const value = baseValue + (Math.random() - 0.5) * variation;
        mockData.push({
          time,
          value: Math.max(0, Number(value.toFixed(2))), // 确保值不为负数
        });
      }

      setChartData(mockData);
    } catch {
      setChartData([]);
    } finally {
      setChartLoading(false);
    }
  };

  // 关闭图表弹窗
  const handleCloseChartModal = () => {
    setChartModalVisible(false);
    setSelectedMonitorItem(null);
    setChartData([]);
  };

  // 空气质量表格列配置
  const airQualityColumns: ColumnsType<AirQualityTableDataType> = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      align: 'center',
      render: (_text, _record, index) => index + 1,
    },
    {
      dataIndex: 'monitorTime',
      title: '监测时间',
      width: 120,
      align: 'center',
    },
    {
      dataIndex: 'airNum',
      title: 'AQI',
      width: 80,
      align: 'center',
    },
    {
      dataIndex: 'airLeavelText',
      title: '空气质量状况',
      width: 120,
      align: 'center',
    },
    {
      dataIndex: 'primaryPollutant',
      title: '首要污染物',
      width: 120,
      align: 'center',
    },
    {
      dataIndex: 'so2Num',
      title: '二氧化硫(μg/m³)',
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'no2Num',
      title: '二氧化氮(μg/m³)',
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'o3Num',
      title: '臭氧(μg/m³)',
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'pm25Num',
      title: '细微颗粒物PM2.5(μg/m³)',
      width: 120,
      align: 'center',
    },
    {
      dataIndex: 'pm10Num',
      title: '可吸入颗粒物PM10(μg/m³)',
      width: 120,
      align: 'center',
    },
    {
      dataIndex: 'coNum',
      title: '一氧化碳(μg/m³)',
      width: 100,
      align: 'center',
    },
    {
      dataIndex: 'fluoride',
      title: '氟化物(μg/m³)',
      width: 120,
      align: 'center',
    },
  ];

  // 查询空气监测信息
  const queryAirMonitorInfo = async (dateRange?: [moment.Moment | null, moment.Moment | null]) => {
    try {
      setAirMonitorLoading(true);
      // 使用传入的时间范围或默认的 rangeDate
      const currentRange = dateRange || rangeDate;
      const params = {
        startTm: currentRange[0]
          ? currentRange[0].startOf('day').format('YYYY-MM-DD HH:mm:ss')
          : '',
        endTm: currentRange[1] ? currentRange[1].endOf('day').format('YYYY-MM-DD HH:mm:ss') : '',
      };
      const res = await analysisApi.queryAirMonitorInfo(params);
      // 处理返回的数据
      if (res.code === 200 && res.data) {
        setAirQuality({ ...res.data, aqiLevel: getAqiLevel(res.data?.airLeavel) });
      } else {
        setAirQuality({});
      }
    } finally {
      setAirMonitorLoading(false);
    }
  };

  // 查询月度监测信息
  const queryMonitorInfoByMonth = async (selectedMonth?: moment.Moment) => {
    try {
      setMonthMonitorLoading(true);
      // 使用传入的月份或默认的 month
      const currentMonth = selectedMonth || month;
      const params = {
        startTm: currentMonth.startOf('month').format('YYYY-MM-DD HH:mm:ss'),
        endTm: currentMonth.endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      };
      const res = await analysisApi.queryMonitorInfoByMonth(params);
      // 处理返回的数据
      if (res.code === 200 && res.data) {
        setAirQualityData(res.data);
      } else {
        setAirQualityData([]);
      }
    } finally {
      setMonthMonitorLoading(false);
    }
  };

  useEffect(() => {
    getMonitorData();
  }, [mainMonitorKey]);

  // 监听时间范围变化，自动查询空气监测信息
  useEffect(() => {
    if (rangeDate && rangeDate[0] && rangeDate[1]) {
      queryAirMonitorInfo(rangeDate);
    }
  }, [rangeDate]);

  // 监听月份变化，自动查询月度监测信息
  useEffect(() => {
    if (month) {
      queryMonitorInfoByMonth(month);
    }
  }, [month]);

  return (
    <div className={style['statistical-index']}>
      <Spin spinning={loading}>
        {/* <div className={style['statistical-index-header']}>
          <div className={style['statistical-index-header-title']}>
            <span>统计分析</span>
          </div>
        </div> */}
        <div className={style['statistical-index-content']}>
          {/* 左侧监测指标 */}
          <div className={style['statistical-index-content-left']}>
            <div className={style['left-header']}>
              <Spin spinning={airMonitorLoading}>
                <div className={style['air-quality-card']}>
                  <div className={style['air-quality-card-date']}>
                    <RangePicker value={rangeDate} onChange={(value) => setRangeDate(value)} />
                  </div>
                  {/* 空气质量 */}
                  <div className={style['air-quality-header']}>
                    <div className={style['air-quality-label']}>
                      <div className={style['leaf-icon']} />
                      <span>空气质量</span>
                      <div className={style['air-quality-level']}>{airQuality.aqiLevel}</div>
                    </div>
                    <div className={style['quality-right']}>
                      <div className={style['quality-item']}>
                        <div className={style['qualitys-value']}>
                          {airQuality.complianceDays}/{airQuality.monitorDays}
                        </div>
                        <div className={style['quality-label']}>合格天数/监测天数</div>
                      </div>
                      <div className={style['quality-item']}>
                        <div className={style['qualitys-value']}>{airQuality.passRate}%</div>
                        <div className={style['quality-label']}>合格率</div>
                      </div>
                      <div className={style['quality-item']}>
                        <div className={style['qualitys-value']}>{airQuality.airNum}</div>
                        <div className={style['quality-label']}>AQI</div>
                      </div>
                    </div>
                  </div>
                  {/* AQI指标 */}
                  <div className={style['aqi-indicator']}>
                    <div className={style['indicator-bar']}>
                      <div
                        className={style['indicator-mask']}
                        style={{ width: `${100 - getAqiPosition(airQuality.airNum || 0)}%` }}
                      />
                      <div
                        className={style['indicator-pointer']}
                        style={{ left: `${getAqiPosition(airQuality.airNum || 0)}%` }}
                      />
                    </div>
                    <div className={style['aqi-legend']}>
                      {levelData.map((item) => (
                        <div className={style['legend-item']} key={`${item.level}-${item.min}`}>
                          <span
                            className={style['legend-color']}
                            style={{ backgroundColor: item.color }}
                          />
                          <span className={style['legend-text']}>
                            {item.level}({item.limit ? `>${item.limit}` : `${item.min}-${item.max}`}
                            )
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </Spin>
              {/* 主要监测指标 */}
              <div className={style['main-monitor-indicator']}>
                <div className={style['main-monitor-indicator-title']}>主要监测指标</div>
                <Tabs
                  defaultActiveKey={mainMonitorKey}
                  onChange={onChange}
                  items={mainMonitor.map((item) => ({
                    label: item.label,
                    key: item.value,
                  }))}
                />
                <div className={style['main-monitor-indicator-content']}>
                  <div className={style['main-monitor-indicator-content-header']}>
                    <Select
                      style={{ width: '35%' }}
                      showSearch
                      optionFilterProp="children"
                      onChange={onEquipChange}
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                      placeholder="请选择"
                      options={[
                        {
                          value: 'jack',
                          label: 'Jack',
                        },
                        {
                          value: 'lucy',
                          label: 'Lucy',
                        },
                        {
                          value: 'tom',
                          label: 'Tom',
                        },
                      ]}
                    />
                    <div className={style['main-monitor-indicator-content-time']}>
                      <span>数据更新时间：{updateTime || '-'}</span>
                    </div>
                  </div>
                  <div className={style['main-monitor-indicator-content-list']}>
                    {mainMonitorData.map((item) => (
                      <div
                        className={style['main-monitor-indicator-content-item']}
                        key={item.code}
                        onClick={() => handleMonitorItemClick(item)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleMonitorItemClick(item);
                          }
                        }}
                        role="button"
                        tabIndex={0}
                        style={{ cursor: 'pointer' }}
                        title={`点击查看${item.name}趋势图`}
                      >
                        <div className={style['main-monitor-indicator-content-item-name']}>
                          {item.name}
                        </div>
                        <div className={style['main-monitor-indicator-content-item-value']}>
                          {item.number}
                        </div>
                        <div className={style['main-monitor-indicator-content-item-unit']}>
                          {item.measureUnit}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* 右侧空气质量情况 */}
          <div className={style['statistical-index-content-right']}>
            <div className={style['right-air-quality']}>
              <div className={style['right-air-quality-chart']}>
                <AirQualityChart
                  selectedMonth={month}
                  loading={monthMonitorLoading}
                  airQualityData={airQualityData}
                  dateRange={rangeDate}
                />
              </div>
              <div className={style['right-air-quality-title']}>
                <span>空气质量情况</span>
                <DatePicker
                  value={month}
                  style={{ marginLeft: 20 }}
                  picker="month"
                  onChange={(value) => {
                    setMonth(value);
                  }}
                />
                <button
                  type="button"
                  className={style['right-air-quality-button']}
                  // TODO:跳转分析报告
                  onClick={() => history.push('/statistical/analysis')}
                >
                  分析报告
                </button>
              </div>
              <Table<AirQualityTableDataType>
                columns={airQualityColumns}
                dataSource={airQualityData}
                rowKey="monitorTime"
                pagination={false}
                scroll={{ x: 1200 }}
                size="small"
                bordered
                loading={airMonitorLoading}
              />
            </div>
          </div>
        </div>
      </Spin>

      {/* 监测数据趋势图弹窗 */}
      {selectedMonitorItem && (
        <MonitorDataChart
          visible={chartModalVisible}
          onClose={handleCloseChartModal}
          monitorInfo={{
            code: selectedMonitorItem.code,
            name: selectedMonitorItem.name,
            measureUnit: selectedMonitorItem.measureUnit,
          }}
          chartData={chartData}
          loading={chartLoading}
        />
      )}
    </div>
  );
};

export default StatisticalIndex;
