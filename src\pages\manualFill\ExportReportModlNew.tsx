import React, { useEffect, useState } from 'react';
import type { IYTHColumnProps } from 'yth-ui/es/components/list/index';
import {
  Modal,
  message,
  Button,
  Popover,
  Empty,
  Checkbox,
  Table,
  Form,
  Select,
  DatePicker,
  Row,
  Col,
} from 'antd';
import moment from 'moment';
import formApi from '@/service/formApi';
import {
  queryUnitInfoByType,
  queryEquipInfoByCompanyId,
  queryMonitorIndex,
  exportManualFillData,
  queryMonitorIndexReportData,
} from '@/service/envApi';
import style from './fill.module.less';

type objType = Record<string, string | number>;

type PropsTypes = {
  open: boolean;
  /** 回调函数 */
  onResult?: (result?: objType) => void;
  /** 根据不同类型 导出时文件名不同 */
  exportType: 'envQuality' | 'pollutionSource';
  /** 传入的 环境质量和污染源 字典值 */
  monitorData: 'A22A08A06' | 'A22A08A07';
};

/** 报表类型 types */
type ReportTypes = {
  name: '日报表' | '月报表';
  code: 'date' | 'month';
  key: string;
};

const lastCol = {
  cou: '个数',
  min: '最小值',
  max: '最大值',
  mean: '均值',
  total: '合计',
};

type collistType = {
  code: string;
  id: string;
  [key: string]: string;
};

const dataformat = [
  { value: '均值', key: 'avg' },
  { value: '累计值', key: 'sum' },
];

// 报表类型
const reportTypeData: ReportTypes[] = [
  { name: '日报表', code: 'date', key: '1' },
  { name: '月报表', code: 'month', key: '2' },
];

/**
 * @description 导出modal
 * @returns
 */
const exportReportModl: React.FC<PropsTypes> = ({ open, onResult, exportType, monitorData }) => {
  const [form] = Form.useForm();
  const [checkValue, setCheckValue] = useState<React.Key[]>([]); // 因子配置选择的可见表头项
  const [columnList, setColumnList] = useState<objType[]>([]); // 原始column表头数据
  const [filterCol, setFilterCol] = useState<IYTHColumnProps[]>([]); // 根据因子配置筛选的column数据
  const [resData, setResData] = useState<objType[]>([]); // 列表返回数据
  const [loading, setLoading] = useState<boolean>(false);
  const [reportType, setReportType] = useState<ReportTypes['code']>('date'); // 报表类型
  const [showEnd, setShowEnd] = useState<boolean>(false); // 根据条件判断是否展示结束时间
  const [deviceDataList, setDeviceDataList] = useState<collistType[]>([]);
  const [monitorList, setMonitorList] = useState<objType[]>([]); // 监测类型
  const [companyList, setCompanyList] = useState<objType[]>();
  const [frequencyList, setFrequencyList] = useState<objType[]>([]); // 采集频率
  const [dataSourceTypeKey, setDataSourceTypeKey] = useState<string>(''); // 数据源

  // 获取所属单位
  const getCompanyList = async () => {
    const data = await queryUnitInfoByType(monitorData);
    if (data.code === 200 && data.data instanceof Array && data.data.length > 0) {
      setCompanyList([...data.data]);
    }
  };
  // 获取采集频率
  const getFrequencyList = async () => {
    const { list } = await formApi.getDictionary({
      condition: {
        fatherCode: 'A23A04',
      },
      currentPage: 0,
      pageSize: 0,
    });
    setFrequencyList([...list]);
    form.setFieldsValue({ dataSourceType: list[0]?.remark || '' });
    setDataSourceTypeKey(list[0]?.remark || '');
  };

  /**
   * @description 获取监测类型
   */
  const getMonitorList = async () => {
    const { list } = await formApi.getDictionary({
      condition: {
        fatherCode: monitorData,
      },
      currentPage: 0,
      pageSize: 0,
    });
    if (monitorData === 'A22A08A07') {
      const newList = list?.filter((item) => item.code !== 'A22A08A07A05') || [];
      setMonitorList([...newList]);
    } else {
      setMonitorList([...list]);
    }
  };

  /**
   * @description 固定表头
   */
  const fixedCol: IYTHColumnProps[] = [
    {
      code: 'collectTime',
      name: '监测时间',
      width: 180,
      title: '监测时间',
      align: 'center',
      dataIndex: 'collectTime',
      sorter: true,
      render: (_r, record) => {
        if (record.stat) {
          return lastCol[record.stat];
        }
        return record.monitorTime || '-';
      },
      fixed: 'left',
      key: 'collectTime',
    },
  ];

  /**
   * @description 格式化数据
   * @param treeData
   * @returns
   */
  type dealTreeDataType = (
    list?: Array<{
      code?: string;
      name?: string;
      measureUnit?: string;
      firstLevelMax?: string;
      firstLevelMin?: string;
      isTotalizeValue?: string;
    }>,
  ) => IYTHColumnProps[];
  const dealTreeData: dealTreeDataType = (treeData) => {
    const data: IYTHColumnProps[] = treeData?.map((item, index) => {
      const newItem: IYTHColumnProps = {
        ...item,
        key: (item.code as string) + index,
        dataIndex: (item.code as string) + index,
        title: item.name,
        width: 180,
        align: 'center',
        children: [],
      };
      newItem.children = dataformat.map((str) => {
        return {
          key: item.code + str.key + index,
          dataIndex: item.code + str.key + index,
          align: 'center',
          title: str.value,
          children: [
            {
              key: item.code + str.key,
              dataIndex: item.code + str.key,
              align: 'center',
              title: item.measureUnit,
              render: (_r, record) => {
                const newVal = item.code + str.key;
                if (record[newVal]) {
                  if (record.stat) {
                    return record[newVal] ? Math.trunc(Number(record[newVal]) * 1000) / 1000 : '-';
                  }
                  if (
                    item.firstLevelMax &&
                    item.firstLevelMin &&
                    (record[newVal] > item.firstLevelMax || record[newVal] < item.firstLevelMin)
                  ) {
                    return (
                      <div className={style.triangle}>
                        {record[newVal] ? Math.trunc(Number(record[newVal]) * 1000) / 1000 : '-'}
                      </div>
                    );
                  }
                  return record[newVal] ? Math.trunc(Number(record[newVal]) * 1000) / 1000 : '-';
                }
                if (record[newVal] === 0) {
                  return '0';
                }
                return '-';
              },
            },
          ],
        };
      });
      if (!item.isTotalizeValue || item.isTotalizeValue === '0') {
        delete newItem.children[1];
      }

      return newItem;
    });
    return data;
  };

  /**
   * @description 简单格式化时间
   * @param dataType // 报表类型
   * @param dataSource  // 数据源
   * @param startTime
   * @param endTime
   * @returns
   */
  const changeTimeFormat = (
    dataType: ReportTypes['code'],
    dataSource: string,
    startTime,
    endTime,
  ) => {
    let newStart = '';
    let newEnd = '';
    if (dataType === 'month') {
      newStart = moment(startTime).startOf('month').format('YYYY-MM-DD HH:mm:ss');
      newEnd = moment(startTime).endOf('month').format('YYYY-MM-DD HH:mm:ss');
    } else {
      switch (dataSource) {
        // 查找数据源对应的配置，处理年份类型
        case '9999':
          newStart = moment(startTime).startOf('year').format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(startTime).endOf('year').format('YYYY-MM-DD HH:mm:ss');
          break;
        // 日数据
        case '2031':
          newStart = moment(startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          break;
        // 小时数据 实时数据
        default:
          newStart = moment(startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss');
          newEnd = moment(startTime).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          break;
      }
    }
    return { newStart, newEnd };
  };

  /**
   * @description 获取列表数据
   * @param list
   */
  const getDataList: (id?: string) => void = async (id) => {
    const { startDate, endDate, deviceCode, formCompany, monitorType, dataSourceType } =
      form.getFieldsValue();
    const aaa = {
      dataType: reportType,
      dataSource: dataSourceType ?? '',
      startTime: startDate,
      endTime: endDate,
    };
    const equipId = id ?? deviceDataList.find((str) => str.code === deviceCode)?.id;
    const { newStart, newEnd } = changeTimeFormat(
      aaa.dataType,
      aaa.dataSource,
      aaa.startTime,
      aaa.endTime,
    );
    setLoading(true);
    const responseData = await queryMonitorIndexReportData({
      monitorType,
      equipCd: deviceCode || '',
      equipId: equipId ?? '',
      orgCd: formCompany,
      startTm: newStart,
      endTm: newEnd,
      dateType: reportType === 'month' ? '4' : dataSourceType,
    });
    if (responseData?.code && responseData?.code === 200 && responseData?.data) {
      setResData(Array.isArray(responseData.data) ? [...responseData.data] : []);
    }
    setLoading(false);
  };

  /**
   * @description 获取表头column数据 并更改结构
   */
  const getColData = async (list?: collistType[]) => {
    const { deviceCode } = form.getFieldsValue();
    const dataList: collistType[] = list || deviceDataList;
    const equipId = dataList.find((str: collistType) => str.code === deviceCode)?.id || '';
    setLoading(true);
    const data = await queryMonitorIndex(equipId);
    if (data.code === 200 && data.data instanceof Array && data.data.length > 0) {
      const newList = data?.data.map((item) => item.code) as string[];
      const newData = data.data as objType[];
      setColumnList([...newData]);
      setFilterCol([...fixedCol, ...dealTreeData(data.data)]);
      setCheckValue([...newList]);
      getDataList(equipId);
    } else {
      setColumnList([]);
      setFilterCol([...fixedCol]);
      setCheckValue([]);
    }
    setLoading(false);
  };

  type DeviceDataType = (
    orgData?: objType[],
    monitorListData?: objType[],
    equipCd?: React.Key,
  ) => void;
  /**
   * @description 根据选择公司获取设备
   */
  const getDeviceData: DeviceDataType = async (orgData, monitorListData, equipCd) => {
    const { monitorType, formCompany } = form.getFieldsValue();
    const monitor = monitorType || (monitorListData && monitorListData[0]?.code) || '';
    const companyId = formCompany || orgData[0]?.code || '';
    if (!companyId) return;
    setLoading(true);
    setResData([]);
    const data = await queryEquipInfoByCompanyId({
      companyId,
      type: monitorData,
      monitorOnline: '0',
      monitorType: monitor,
    });
    if (data.code === 200 && data.data instanceof Array && data.data.length > 0) {
      form.setFieldsValue({ deviceCode: equipCd ?? data.data[0]?.code });
      setDeviceDataList([...data.data]);
      getColData(data.data);
    } else {
      form.resetFields(['deviceCode']);
      setDeviceDataList([]);
      setColumnList([]);
      setCheckValue([]);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (open) {
      getCompanyList();
      getMonitorList();
      getFrequencyList();
    }
    return () => {
      setReportType('date');
      setFilterCol([...fixedCol]);
      setResData([]);
      setCheckValue([]);
      setColumnList([]);
      setDeviceDataList([]);
    };
  }, [open]);

  useEffect(() => {
    // 创建Set去重
    const uniqueCodes = new Set(checkValue);
    // 根据选中的因子筛选列
    const newList = [];
    // 使用Set.has方法检查code是否存在
    columnList.forEach((item) => {
      if (uniqueCodes.has(item.code)) {
        newList.push(item);
      }
    });
    setFilterCol([...fixedCol, ...dealTreeData(newList)]);
  }, [JSON.stringify(checkValue)]);

  // 文件导出
  const uploadTemplate = async () => {
    setLoading(true);
    const { startDate, endDate, dataSourceType, formCompany, deviceCode, monitorType } =
      form.getFieldsValue();
    const equipData = deviceDataList.find((device) => device.code === deviceCode);
    const { newStart, newEnd } = changeTimeFormat(reportType, dataSourceType, startDate, endDate);
    const companyName = companyList.find((str) => str.companyId === formCompany)?.supplyUnit || '-';

    if (!equipData) {
      message.error('请选择设备名称');
      setLoading(false);
      return;
    }

    const newList = [...filterCol];
    newList.shift();
    const params = {
      companyName,
      equipName: equipData?.name ?? '',
      equipCd: equipData?.code ?? '',
      equipId: equipData?.id ?? '',
      orgCd: formCompany || '',
      startTm: newStart,
      endTm: newEnd,
      dateType: reportType === 'month' ? '4' : dataSourceType,
      monitorType,
      reportType: reportTypeData.find((r) => r.code === reportType).key,
      monitorIndexHeaderParamList: newList,
    };
    const exportData = await exportManualFillData({
      data: params,
      responseType: 'formData',
    });
    setLoading(false);
    const href = window.URL.createObjectURL(
      new Blob([exportData], { type: 'application/vnd.ms-excel;charset=utf-8' }),
    );
    const link = document.createElement('a');
    link.href = href;
    link.download =
      companyName +
      equipData.name +
      (exportType === 'envQuality' ? '环境质量监测报表' : '污染源监测报表');
    window.document.body.appendChild(link);
    link.click();
    window.document.body.removeChild(link);
    window.URL.revokeObjectURL(href);
  };

  /**
   * @description Popover弹出内容content
   */
  const popoverPontent: React.JSX.Element = (
    <div
      style={{
        maxHeight: 300,
        width: 150,
        overflowY: 'auto',
      }}
    >
      {columnList.length >= 1 ? (
        <Checkbox.Group
          value={checkValue}
          onChange={(e) => {
            if (e.length === 0) return;
            setCheckValue(e as string[]);
          }}
        >
          <Row>
            {columnList.map((item) => {
              return (
                <Col key={item.code} span={24}>
                  <Checkbox value={item.code}>{item.name}</Checkbox>
                </Col>
              );
            })}
          </Row>
        </Checkbox.Group>
      ) : (
        <Empty />
      )}
    </div>
  );

  const onCloseModal = () => {
    form.resetFields();
    onResult();
  };

  /**
   * 获取当前应该使用的日期选择器类型
   */
  const getPickerType = (): 'date' | 'month' | 'year' => {
    if (reportType === 'month') {
      return 'month';
    }
    if (dataSourceTypeKey === '9999') {
      return 'year';
    }
    return 'date';
  };

  const disabledStartDate = (current) => {
    const { endDate } = form.getFieldsValue();
    return current && current > endDate;
  };

  const disabledEndDate = (current) => {
    const { startDate } = form.getFieldsValue();
    return current && current < startDate;
  };

  return (
    <div>
      <Modal
        title="报表"
        visible={open}
        key="exportReportModl"
        closable
        width="80%"
        onCancel={onCloseModal}
        footer={null}
        className={style['manual-export-modal']}
        destroyOnClose
      >
        <Form
          form={form}
          name="exportReportForm"
          className={style['ant-gasleak-form']}
          onFinish={() => getDataList()}
          initialValues={{
            formReport: reportType,
            startDate: moment(),
            endDate: moment(),
          }}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name="formCompany"
                label="所属单位"
                rules={[{ required: true, message: '请选择所属单位' }]}
              >
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onSearch={() => {
                    getDeviceData();
                  }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={() => {
                    getDeviceData();
                  }}
                >
                  {(companyList || []).map((item) => (
                    <Select.Option key={item.companyId}>{item.supplyUnit || '-'}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name="monitorType" label="监测类型">
                <Select
                  placeholder="请选择"
                  onChange={() => {
                    form.resetFields(['deviceCode']);
                    getDeviceData();
                  }}
                >
                  {monitorList?.map((item) => (
                    <Select.Option key={item.code}>{item.text}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="deviceCode"
                label="设备名称"
                rules={[{ required: true, message: '请选择设备名称' }]}
              >
                <Select
                  placeholder="请选择"
                  showSearch
                  style={{ fontSize: 12 }}
                  onSearch={() => {
                    getColData();
                  }}
                  optionFilterProp="children"
                  filterOption={(input: string, option?: { children: string; value: string }) =>
                    (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  onChange={() => {
                    getColData();
                  }}
                >
                  {(deviceDataList || []).map((item) => (
                    <Select.Option key={item.code}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* <Col span={6}>
              <Form.Item name="formReport" label="类型">
                <Select
                  placeholder="请选择"
                  onChange={(value) => {
                    setReportType(value);
                  }}
                >
                  {reportTypeData.map((item) => (
                    <Select.Option key={item.code}>{item.name}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col> */}
            {reportType === 'date' && (
              <Col span={6}>
                <Form.Item name="dataSourceType" label="采集频率">
                  <Select
                    placeholder="请选择"
                    onSelect={(e) => {
                      setDataSourceTypeKey(e);
                      // 2031 为日数据
                      if (e === '2031') {
                        setShowEnd(true);
                      } else {
                        setShowEnd(false);
                      }
                    }}
                  >
                    {frequencyList.map((item) => (
                      <Select.Option key={item.remark}>{item.text}</Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            )}
            <Col span={8} style={{ display: 'flex' }}>
              <Form.Item name="startDate" label="监测时间">
                <DatePicker
                  style={{ width: '100%' }}
                  picker={getPickerType()}
                  disabledDate={disabledStartDate}
                />
              </Form.Item>
              {reportType === 'date' && showEnd && (
                <Form.Item name="endDate" label="至">
                  <DatePicker
                    style={{ width: '100%' }}
                    picker="date"
                    disabledDate={disabledEndDate}
                  />
                </Form.Item>
              )}
            </Col>
            <Col span={16} style={{ textAlign: 'end' }}>
              <Button className={style['top-button']} type="primary" htmlType="submit">
                查询
              </Button>
              <Button
                style={{ marginLeft: 15 }}
                className={style['top-button']}
                type="primary"
                onClick={uploadTemplate}
              >
                导出Excel
              </Button>
            </Col>
          </Row>
        </Form>

        <div className={style['export-manual-popover']}>
          <Popover
            placement="bottomRight"
            overlayClassName={style['export-popover-content']}
            trigger="click"
            title={null}
            content={popoverPontent}
          >
            <Button type="primary" size="small" ghost>
              因子配置
            </Button>
          </Popover>
        </div>
        <Table
          loading={loading}
          bordered
          rowKey={(row) => row.id}
          dataSource={resData}
          className={style['export-table']}
          rowClassName={(_, index) => {
            if (index % 2 === 0) {
              return style['even-row'];
            }
            return style['odd-row'];
          }}
          columns={filterCol}
          scroll={{ x: checkValue.length * 180, y: 450 }}
        />
      </Modal>
    </div>
  );
};
export default exportReportModl;
