import moment from 'moment';
import { DateListTypes, MonitorTypeConfig, OnlineMonitorTagType } from './types';

/**
 * 时间类型列表
 * 包含不同时间类型的配置信息
 * 里面的key值会被采集频率字典数据中的枚举值替换
 * text 会被采集频率字典数据中的text替换
 */
export const dateList: DateListTypes[] = [
  {
    text: '实时数据', // 时间类型文本
    dataCode: 'realTime', // 时间类型代码
    key: '1', // 后端接口使用的键值
    format: 'YYYY-MM-DD HH:mm:ss', // 日期格式
    over: 7, // 时间范围限制(天)
    pickerType: 'dateTime', // 日期时间选择器类型
    defaultRange: () => [moment().startOf('day'), moment()], // 默认时间范围：当天0点到现在
  },
  {
    text: '时均值', // 时间类型文本
    dataCode: 'hour', // 时间类型代码
    key: '2', // 后端接口使用的键值
    format: 'YYYY-MM-DD HH', // 日期格式
    over: 7, // 时间范围限制(天)
    pickerType: 'dateTime', // 日期时间选择器类型
    defaultRange: () => [moment().startOf('day'), moment().endOf('hour')], // 默认时间范围：当天0点到当前小时结束
  },
  {
    text: '日均值', // 时间类型文本
    dataCode: 'daily', // 时间类型代码
    key: '3', // 后端接口使用的键值
    format: 'YYYY-MM-DD', // 日期格式
    over: 30, // 时间范围限制(天)
    pickerType: 'date', // 日期选择器类型
    defaultRange: () => [moment().subtract(7, 'days').startOf('day'), moment().endOf('day')], // 默认时间范围：7天前到今天为止
  },
  {
    text: '年均值', // 时间类型文本
    dataCode: 'year', // 时间类型代码
    key: '4', // 后端接口使用的键值
    format: 'YYYY', // 日期格式
    // over: 3650, // 时间范围限制(天)，约10年
    pickerType: 'year', // 年份选择器类型
    defaultRange: () => moment(), // 默认时间范围：当年的开始到结束
  },
];

/**
 * 均值和累计值数据标识
 * 用于表格列显示和数据处理
 */
export const dataformat = [
  { value: '均值', key: 'avg' }, // 均值数据标识
  { value: '累计值', key: 'sum' }, // 累计值数据标识
];

/**
 * 监测类型配置映射
 * 不同监测类型的配置信息
 */
export const monitorTypeConfigs: Record<string, MonitorTypeConfig> = {
  // 环境质量监测
  envQuality: {
    onLineMonitorType: 'Air', // 监测类型值，传递给后端接口
    exportName: '环境质量监测', // 导出报表名称
    monitorKey: 'A22A08A06', // 字典键值，用于获取监测类型列表
  },

  // 污染源监测
  wasteGas: {
    onLineMonitorType: 'wasteAir', // 监测类型值，传递给后端接口
    exportName: '污染源监测', // 导出报表名称
    filterMonitorList: true, // 是否过滤监测类型列表
    filterCondition: (item) => item.code !== 'A22A08A07A05', // 过滤条件：排除公用工程监测类型
    monitorKey: 'A22A08A07', // 字典键值，用于获取监测类型列表
  },

  // 公用工程监测
  utilityMonitor: {
    onLineMonitorType: 'wasteAir', // 监测类型值，传递给后端接口
    exportName: '公用工程监测', // 导出报表名称
    filterMonitorList: true, // 是否过滤监测类型列表
    filterCondition: (item) => item.code === 'A22A08A07A05', // 过滤条件：只包含公用工程监测类型
    monitorKey: 'A22A08A03', // 字典键值，用于获取监测类型列表 注意公用工程监测的做特殊处理
  },
};

/**
 * 在线监测标签列表
 * 不同标签的配置信息，颜色代表不同含义：
 * - 白色：正常状态（正常）
 * - 绿色：维护状态（维护）
 * - 红色：严重异常（停运、故障）
 * - 橙色：警告状态（校准）
 * - 蓝色：人工操作（手工输入）
 * - 紫色：数据异常（超上限）
 * - 灰色：通信问题（通信异常）
 */
export const tagList: OnlineMonitorTagType[] = [
  {
    text: '正常',
    key: 'N',
    color: '#FFFFFF', // 保持原来的白色
    borderColor: '#000000',
  },
  {
    text: '停运',
    key: 'F',
    color: '#F32430', // 保持原来的红色 - 表示设备停止运行
  },
  {
    text: '维护',
    key: 'M',
    color: '#40AB3E', // 保持原来的绿色 - 表示设备维护中
  },
  {
    text: '手工输入',
    key: 'S',
    color: '#1890FF', // 蓝色 - 表示人工录入数据
  },
  {
    text: '故障',
    key: 'D',
    color: '#FF4D4F', // 红色 - 表示设备故障
  },
  {
    text: '校准',
    key: 'C',
    color: '#FA8C16', // 橙色 - 表示设备校准中
  },
  {
    text: '超上限',
    key: 'T',
    color: '#722ED1', // 紫色 - 表示数据超出上限
  },
  {
    text: '通信异常',
    key: 'B',
    color: '#8C8C8C', // 灰色 - 表示通信故障
  },
];
