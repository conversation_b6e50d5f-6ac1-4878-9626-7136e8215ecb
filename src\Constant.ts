import { isEmpty, YTHLocalization } from 'yth-ui';

export const LANGUAGE_STORE_KEY: string = '$_language';

export const CurrentUser: () => Record<string, string> = () => {
  return JSON.parse(window.sessionStorage.getItem('$_user') || '{}');
};

export const Token: () => string = () => {
  // return window.sessionStorage.getItem('$_token') || '';
  return 'bearer af2cc537-cacf-4606-9037-4c6bb588c126';
};

export const Setting: () => Record<string, string> = () => {
  return JSON.parse(window.localStorage.getItem('yth_form_config_setting') || '{}');
};

/**
 * 配置request请求时的默认参数
 */
export const ConstHeaders = () => {
  const { affinityHost = '' } = Setting();
  const headers = new Headers();
  headers.append('Content-Language', YTHLocalization.getLanguage());
  if (Token()) {
    headers.append('Authorization', Token() || '');
  }
  if (!isEmpty(affinityHost)) {
    headers.append('affinity_host', affinityHost);
  }
  return headers;
};
